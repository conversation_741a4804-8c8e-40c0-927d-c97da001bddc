export const classNames = {
  fileUpload: {
    label: "font-medium",
    input:
      "block w-full text-sm file:mr-4 py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-pink-50 file:text-pink-700",
  },
  input: {
    label: "font-medium text-default-700",
    inputWrapper: "bg-white dark:bg-inherit",
    // inputWrapper:
    //   "group-data-[focus=true]:ring-primary-300 group-data-[focus=true]:ring-2  group-data-[focus=true]:border-default-200 data-[hover=true]:border-primary-200",
  },
} as const;

export const baseUrl = process.env.NEXT_PUBLIC_APPS_URL ?? "";

export const FALLBACK_PROFILE = `/default-profile.png`;

export const PATIENT_TYPE_LIST = [
  { value: "mahasiswa", label: "Mahasiswa" },
  { value: "pegawai", label: "Pegawai" },
  { value: "keluarga-pegawai", label: "Ke<PERSON>arga Pegawai" },
  { value: "umum", label: "Umum" },
  { value: "alumni", label: "Alumni" },
  { value: "pensiunan", label: "Pensiunan" },
];

export const PATIENT_TYPE = {
  mahasiswa: "Mahasiswa",
  pegawai: "Pegawai",
  "keluarga-pegawai": "Keluarga Pegawai",
  umum: "Umum",
  alumni: "Alumni",
  pensiunan: "Pensiunan",
};
