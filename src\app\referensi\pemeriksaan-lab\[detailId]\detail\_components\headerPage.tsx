'use client'

import { <PERSON><PERSON>, Too<PERSON><PERSON> } from "@heroui/react";
import { Pencil } from "lucide-react";
import { useRouter } from "next/navigation";


export default function PageHeader({id} : {id :string}) {
    const router = useRouter()
    return (
        <div className="mb-4 flex items-center justify-between">
            <h1 className="font-medium lg:text-xl">Detail Pemeriksaan Darah Lengkap</h1>
            <Tooltip content="Edit Data">
                <Button
                    startContent={<Pencil className="w-4" />}
                    color="primary"
                    size="sm"
                    onPress={() => router.push(`/referensi/pemeriksaan-lab/${id}/edit`)}
                >
                    Edit
                </Button>
            </Tooltip>
        </div>
    )
}