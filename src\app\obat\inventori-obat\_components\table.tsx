"use client";

import UTable from "@/components/custom/table";
import {
  addToast,
  <PERSON>ton,
  <PERSON>dal,
  Spinner,
  Switch,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
} from "@heroui/react";
import { use, useCallback, useState } from "react";
// import { ResponsePoli } from "../_schema/typePoli";
// import Modals from "./modal";
// import ModalPost from "./modalPost";
// import { patchtPoli } from "../actions";
import { useRouter } from "next/navigation";
import Modals from "./modal";
import ModalPost from "./modalPost";
import { Eye } from "lucide-react";

const dummyApi = [
  {
    id: 1,
    name: "Obat A",
    bentuk: "Tablet",
    satuan: "Buah",
    jenis: "Obat Bebas",
    totalStok: "10",
    almostExpired: "1",
    expired: "0",
  },
  {
    id: 2,
    name: "<PERSON><PERSON> B",
    bentuk: "Tablet",
    satuan: "<PERSON>uah",
    jenis: "<PERSON>bat Bebas",
    totalStok: "10",
    almostExpired: "0",
    expired: "1",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Obat",
  },
  {
    key: "bentuk",
    label: "Bentuk",
  },
  {
    key: "satuan",
    label: "Satuan",
  },
  {
    key: "jenis",
    label: "Jenis",
  },
  {
    key: "totalStok",
    label: "Total Stok",
  },
  {
    key: "almostExpired",
    label: "Obat Hampir Expired",
  },
  {
    key: "expired",
    label: "Obat  Expired",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TableObat({ poli }: Props) {
  const [page, setPage] = useState(0);
  const [sizePage, setSizePage] = useState(10);
  // const data: ResponsePoli = use(poli);
  const router = useRouter();

  const pagination = {
    page: page, // current page
    pageSize: sizePage, // banyak data list
    // totalPage: data?.page?.total_pages ?? 1, //
    // totalData: data?.page?.total_elements ?? 10,
    totalPage: 1, //
    totalData: 10,
    onChangePage: (page: number, pageSize: number) => {
      setPage(page);
      setSizePage(pageSize);
      router.push(`/referensi/poli?page=${page}&size=${pageSize}`);
    },
  };

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    // try {
    //   const res = await patchtPoli({ active: !currentSwitch, id: id });
    //   return res;
    // } catch (error: any) {
    //   addToast({
    //     title: "Error",
    //     color: "danger",
    //     description: error.message ?? "Terjadi kesalahan",
    //   });
    // }
  };

  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <p className="lg:w-[150px]  ">{item.name}</p>;

        case "almostExpired":
          return (
            <p
              className={`lg:w-[40px] py-2 px-4 rounded  ${
                item.almostExpired >= 1
                  ? "bg-yellow-200 font-bold text-yellow-600"
                  : ""
              } `}
            >
              {item.almostExpired}
            </p>
          );
        case "expired":
          return (
            <p
              className={` lg:w-[40px] py-2 px-4 rounded  ${
                item.expired >= 1
                  ? "bg-danger-200 font-bold  text-red-600"
                  : "text-black"
              } `}
            >
              {item.expired}
            </p>
          );
        case "action":
          return (
            <Tooltip content="Edit Data">
              <Button
                isIconOnly
                color="secondary"
                size="sm"
                onPress={() => {
                  router.push(`/obat/inventori-obat/${item.id}`);
                }}
              >
                <Eye className="w-4" />
              </Button>
            </Tooltip>
          );
        default:
          return item[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Poli</h1>
        <ModalPost />
      </div>
      <UTable pagination={pagination} bordered={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              className={`text-black ${
                column.key === "expired" || column.key === "almostExpired"
                  ? "lg:w-[10px] "
                  : ""
              }`}
              align={
                column.key === "action" || column.key === "active"
                  ? "end"
                  : "start"
              }
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={dummyApi ?? []}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {dummyApi.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
      <div className="">
        <p className="font-medium mb-4">Keterangan</p>
        <div className="flex items-center gap-4 mb-2">
          <span className="block bg-danger-200 rounded h-5 w-5" />
          <p className="text-sm">Sudah melewati tanggal kadaluarsa</p>
        </div>
        <div className="flex items-center gap-4">
          <span className="block bg-yellow-200 rounded h-5 w-5" />
          <p className="text-sm">Hampir mendekati tanggal kadaluarsa</p>
        </div>
      </div>
    </div>
  );
}
