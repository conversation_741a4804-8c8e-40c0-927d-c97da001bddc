"use client";

import UTable from "@/components/custom/table";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Spinner,
  Switch,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { Plus } from "lucide-react";
import { useCallback, useState } from "react";
import Modals from "./modal";
import ModalPost from "./modalPost";

export const users = [
  {
    id: 1,
    name: "<PERSON>",
    tugas: ["Poli Umum", "<PERSON>i Gigi", "<PERSON><PERSON> Anak"],
    jenis: "<PERSON><PERSON><PERSON>",
  },
  {
    id: 2,
    name: "<PERSON>",
    tugas: ["<PERSON><PERSON> Umum", "<PERSON><PERSON> Gigi"],
    jenis: "Analisa Lab",
  },
  {
    id: 3,
    name: "<PERSON>",
    tugas: ["Poli Umum"],
    jenis: "Analisa Lab",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "<PERSON>a Tenaga Medis",
  },
  {
    key: "jenis",
    label: "<PERSON><PERSON>",
  },
  {
    key: "tugas",
    label: "<PERSON><PERSON><PERSON>",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableDokter() {
  const [page, setPage] = useState(0);

  const pagination = {
    page: 1,
    pageSize: 10,
    totalPage: 2,
    totalData: 20,
    onChangePage: (page: number, pageSize: number) => setPage(page),
  };

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div>{category.name}</div>;
        case "tugas":
          return (
            <div className="flex flex-wrap  gap-2">
              {category.tugas.map((item: any, index: number) => (
                <Chip key={index} size="sm">
                  {item}
                </Chip>
              ))}
            </div>
          );
        case "action":
          return <Modals id={category.id} />;
        default:
          return category[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Dokter yang Bertugas</h1>
        <ModalPost />
      </div>
      <UTable pagination={pagination} bordered={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={column.key === "action" ? "end" : "start"}
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={users}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {users.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
    </div>
  );
}
