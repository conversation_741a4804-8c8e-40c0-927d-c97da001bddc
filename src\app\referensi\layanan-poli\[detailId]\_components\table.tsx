"use client";

import UTable from "@/components/custom/table";
import {
  <PERSON><PERSON>,
  <PERSON>ner,
  Switch,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
} from "@heroui/react";
import { Pencil, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import ModalPost from "./modalPost";

export const users = [
  {
    id: 1,
    name: "<PERSON>",
    mahasiswa: "gratis",
    pegawai: "10000",
    kel_pegawai: "20000",
    umum: "50000",
    pensiunan: "300000",
    status: "active",
  },
  {
    id: 2,
    name: "<PERSON>",
    mahasiswa: "gratis",
    pegawai: "10000",
    kel_pegawai: "20000",
    umum: "50000",
    pensiunan: "300000",
    status: "active",
  },
  {
    id: 3,
    name: "<PERSON>",
    mahasiswa: "gratis",
    pegawai: "10000",
    kel_pegawai: "20000",
    umum: "50000",
    pensiunan: "300000",
    status: "active",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Layanan",
  },
  {
    key: "mahasiswa",
    label: "Mahasiswa",
  },
  {
    key: "pegawai",
    label: "Pegawai",
  },
  {
    key: "kel_pegawai",
    label: "Kel-Pegawai",
  },
  {
    key: "umum",
    label: "Umum",
  },
  {
    key: "pensiunan",
    label: "Pensiunan",
  },
  {
    key: "status",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableLayananPoliDetail({
  detailId,
}: {
  detailId: string;
}) {
  const [page, setPage] = useState(0);
  const router = useRouter();

  const pagination = {
    page: 1,
    pageSize: 10,
    totalPage: 2,
    totalData: 20,
    onChangePage: (page: number, pageSize: number) => setPage(page),
  };

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "status":
          return (
            <div className="flex gap-2 justify-start items-start ">
              <Switch
                size="sm"
                defaultSelected={category.status === "active"}
              />
              <p className="capitalize">{category.status}</p>
            </div>
          );
        case "action":
          return (
            <div>
              <Tooltip content="Lihat Detail">
                <Button
                  isIconOnly
                  color="secondary"
                  size="sm"
                  onPress={() =>
                    router.push(
                      `/referensi/layanan-poli/${detailId}/update/${category.id}`
                    )
                  }
                >
                  <Pencil className="w-4" />
                </Button>
              </Tooltip>
            </div>
          );
        default:
          return category[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Layanan</h1>
        <Tooltip content="Edit Data">
          <Button
            startContent={<Plus />}
            color="primary"
            size="sm"
            onPress={() =>
              router.push(`/referensi/layanan-poli/${detailId}/add-layanan`)
            }
          >
            Layanan
          </Button>
        </Tooltip>
      </div>
      <UTable pagination={pagination} bordered={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={column.key === "action" ? "end" : "start"}
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={users}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {users.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
    </div>
  );
}
