"use client";

import UTable from "@/components/custom/table";
import {
  <PERSON><PERSON>,
  <PERSON>,
  Spinner,
  Switch,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
} from "@heroui/react";
import { Eye, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";

export const users = [
  {
    id: 1,
    name: "<PERSON>",
    mahasiswa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["<PERSON><PERSON><PERSON><PERSON>", "Pegawai", "<PERSON>", "Um<PERSON>", "<PERSON><PERSON><PERSON><PERSON> Mahasiswa"],
    pensiunan: "20000",
    status: "active",
  },
  {
    id: 2,
    name: "<PERSON>",
    mahasiswa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>awa<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
    pensiunan: "20000",
    status: "active",
  },
  {
    id: 3,
    name: "<PERSON>",
    mahasiswa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["Mahasisawa", "Pegawai", "Alumni", "Umum", "Keluarga Mahasiswa"],
    pensiunan: "20000",
    status: "active",
  },

];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Poli",
  },
  {
    key: "mahasiswa",
    label: "Mahasiswa",
  },
  {
    key: "pegawai",
    label: "Pegawai",
  },
  {
    key: "kel-pegawai",
    label: "Kel.Pegawai",
  },
  {
    key: "umum",
    label: "Umum",
  },
  {
    key: "pensiunan",
    label: "Pensiunan",
  },
  {
    key: "status",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableDetailPemeriksaanLab() {
  const [page, setPage] = useState(0);
  const router = useRouter();
  const navigateAddLayanan = () => router.push(`/referensi/pemeriksaan-lab/tambah-layanan`)
  const pagination = {
    page: 1,
    pageSize: 10,
    totalPage: 2,
    totalData: 20,
    onChangePage: (page: number, pageSize: number) => setPage(page),
  };

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return (
            <div className="flex flex-col gap-1">
              <p>{category.name}</p>
             <div className="flex items-center gap-2">
               {category.list.map((item: any, index: number) => (
                <Chip key={index} size="sm">
                  {item}
                </Chip>
              ))}
             </div>
            </div>
          )
        case "status":
          return (
            <div className="flex gap-2 justify-end items-center ">
              <Switch
                size="sm"
                defaultSelected={category.status === "active"}
              />
              <p className="capitalize">{category.status}</p>
            </div>
          );
        case "action":
          return (
            <div>
              <Tooltip content="Lihat Detail">
                <Button
                  isIconOnly
                  color="secondary"
                  size="sm"
                  onPress={() =>
                    router.push(`/referensi/pemeriksaan-lab/${category.id}/detail`)
                  }
                >
                  <Eye className="w-4" />
                </Button>
              </Tooltip>
            </div>
          );
        default:
          return category[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Pemeriksaan Laboratorium Patologi Klinik</h1>
        <Tooltip content="Tambah Layanan">
          <Button
            startContent={<Plus />}
            color="default"
            className="bg-default-50 border-2 "
            size="sm"
            onPress={navigateAddLayanan}
          >
            Layanan
          </Button>
        </Tooltip>
      </div>
      <UTable pagination={pagination} bordered={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={
                column.key === "action" || column.key === "status"
                  ? "end"
                  : "start"
              }
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={users}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {users.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
    </div>
  );
}
