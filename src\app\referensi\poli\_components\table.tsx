"use client";

import UTable from "@/components/custom/table";
import {
  addToast,
  Spinner,
  Switch,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { use, useCallback, useState } from "react";
import { ResponsePoli } from "../_schema/typePoli";
import Modals from "./modal";
import ModalPost from "./modalPost";
import { patchtPoli } from "../actions";
import { useRouter } from "next/navigation";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Poli",
  },
  {
    key: "active",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TablePoli({ poli }: Props) {
  const [page, setPage] = useState(0);
  const [sizePage, setSizePage] = useState(10);
  const data: ResponsePoli = use(poli);
  const router = useRouter();

  const pagination = {
    page: page, // current page
    pageSize: sizePage, // banyak data list
    totalPage: data?.page?.total_pages ?? 1, //
    totalData: data?.page?.total_elements ?? 10,
    onChangePage: (page: number, pageSize: number) => {
      setPage(page);
      setSizePage(pageSize);
      router.push(`/referensi/poli?page=${page}&size=${pageSize}`);
    },
  };

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    try {
      const res = await patchtPoli({ active: !currentSwitch, id: id });
      return res;
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    }
  };

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{category.name}</div>;
        case "active":
          return (
            <div className="flex gap-2 justify-end items-center ">
              <Switch
                size="sm"
                onValueChange={() =>
                  handleChangeSwitch({
                    id: category.id,
                    currentSwitch: category.active,
                  })
                }
                // isSelected={category?.active}
                defaultSelected={category.active}
              />
              <p className="capitalize">
                {category.active ? "aktif" : "tidak aktif"}
              </p>
            </div>
          );
        case "action":
          return <Modals id={category.id} />;
        default:
          return category[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Poli</h1>
        <ModalPost />
      </div>
      <UTable pagination={pagination} bordered={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={
                column.key === "action" || column.key === "active"
                  ? "end"
                  : "start"
              }
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={data.content ?? []}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {data?.content?.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
    </div>
  );
}
