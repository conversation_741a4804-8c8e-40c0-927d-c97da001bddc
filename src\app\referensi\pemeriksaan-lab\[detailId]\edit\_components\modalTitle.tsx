"use client";

import { FormInput } from "@/components/custom/input";
import {
  addToast,
  <PERSON>ton,
  Modal,
  ModalBody,
  <PERSON>dal<PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus, SquarePen } from "lucide-react";
import { addTitleYup } from "../schema/addLayanan";

type payload = {
  name: string;
};

export default function ModalTitle({
  id,
  updateTitle,
  defaultTitle
}: {
  id: string;
  updateTitle: (parameterId: string, title: string) => void;
  defaultTitle: string
}) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    name: defaultTitle,
  };

  const handleSubmit = async (value: typeof initialValues) => {
    updateTitle(id, value.name);
    onOpenChange();
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: addTitleYup,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Button
        isIconOnly
        color="default"
        size="sm"
        className="bg-default-50"
        onPress={onOpenChange}
      >
        <SquarePen className="w-5 text-blue-500" />
      </Button>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Poli
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-1">
                    <label htmlFor="name" className="text-sm ">
                      {" "}
                      Nama Parameter
                    </label>
                    <FormInput
                      isClearable={false}
                      name={"name"}
                      isNumeric={false}
                      formik={formik}
                      placeholder="Ketik nama parameter"
                    />
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
