"use client";

import { FormInput } from "@/components/custom/input";
import {
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";
import { useState } from "react";
// import { layananPoliPost } from "../schema/postLayananPoli";

type payload = {
  laboratorium: string;
};

export default function ModalPost() {
  const [isSubmit, setIsSubmit] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    laboratorium: "",
  };

  const handleSubmit = (value: typeof initialValues) => {
    setIsSubmit(true);
    alert(JSON.stringify(value));
    setIsSubmit(false);
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    // validationSchema: layananPoliPost,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Edit Data">
        <Button
          startContent={<Plus />}
          color="primary"
          size="sm"
          onPress={onOpenChange}
        >
          Layanan
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Laboratorium
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-1">
                    <label htmlFor="laboratorium" className="text-sm ">
                      {" "}
                      Nama Laboratorium
                    </label>
                    <FormInput
                      name={"laboratorium"}
                      isNumeric={false}
                      formik={formik}
                      placeholder="Ketik nama laboratorium"
                    />
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button color="primary" type="submit" isLoading={isSubmit}>
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
