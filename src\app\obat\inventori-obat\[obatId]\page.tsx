"use client";

import { useFormik } from "formik";
import React from "react";
import { postPenambahanObat } from "./schema/postPenambahanObat";
import {
  FilterDatePicker,
  FormDatePicker,
} from "@/components/custom/datepicker";
import { FormInput } from "@/components/custom/input";
import { DateValue } from "@internationalized/date";
import { Button } from "@heroui/react";
import { Plus, Trash2 } from "lucide-react";

export type listObat = {
  id: string;
  name: string;
  quantityIn: number;
  tglExpired: DateValue | null;
  perusahaan: string;
};

type payload = {
  tglMasuk: DateValue | null;
  noBatch: string;
  listObat: Array<listObat>;
};

const initialList = {
  id: `obat-${Date.now()}`,
  name: "",
  quantityIn: 0,
  tglExpired: null,
  perusahaan: "",
};

export default function PageDetailInventoriObat() {
  const handleSubmit = async (value: payload) => {
    alert(JSON.stringify(value));
  };

  const initialValues = {
    tglMasuk: null,
    noBatch: "",
    listObat: [initialList],
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: postPenambahanObat,
    onSubmit: handleSubmit,
  });

  // Function to add new obat item
  const addObatItem = () => {
    const newObat: listObat = initialList;

    formik.setFieldValue("listObat", [...formik.values.listObat, newObat]);
  };

  // Function to remove obat item
  const removeObatItem = (index: number) => {
    const updatedList = formik.values.listObat.filter((_, i) => i !== index);
    formik.setFieldValue("listObat", updatedList);
  };

  // Function to update specific obat item
  const updateObatItem = (index: number, field: keyof listObat, value: any) => {
    const updatedList = [...formik.values.listObat];
    updatedList[index] = { ...updatedList[index], [field]: value };
    formik.setFieldValue("listObat", updatedList);
  };

  return (
    <div className="w-full flex justify-center">
      <div className="bg-default-50 flex flex-col gap-6 p-5 rounded-md lg:w-1/2 w-full ">
        <h1 className="text-xl font-medium mb-2">
          Form Tambah Penambahan Obat Massal
        </h1>
        <form onSubmit={formik.handleSubmit} className="flex flex-col gap-6">
          {/* Basic Information */}
          <div className="flex items-center gap-6 border-b-3 border-default-400 pb-8">
            <div className="flex flex-col gap-1 w-full">
              <label htmlFor="tglMasuk" className="text-sm">
                Tanggal Perolehan
              </label>
              <FilterDatePicker name="tglMasuk" formik={formik} />
            </div>
            <div className="flex flex-col gap-1 w-full">
              <label htmlFor="noBatch" className="text-sm">
                No. Batch
              </label>
              <FormInput
                isClearable={false}
                name={"noBatch"}
                isNumeric={false}
                formik={formik}
                placeholder="Ketik No. Batch obat masuk"
              />
            </div>
          </div>

          {/* Dynamic List Obat Section */}
          <div className="flex flex-col gap-4">
            <div className=""></div>
            {/* List of Obat Items */}
            <div className="flex flex-col gap-4">
              {formik.values.listObat.map((obat, index) => (
                <div
                  key={obat.id}
                  className="border border-gray-200 rounded-lg flex flex-col gap-6 p-4 bg-default-50"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium ">Obat {index + 1}</h3>
                    <Button
                      type="button"
                      color="danger"
                      variant="solid"
                      size="sm"
                      isIconOnly
                      onPress={() => removeObatItem(index)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>

                  <div className=" gap-4">
                    {/* Nama Obat */}
                    <div className="flex flex-col gap-1">
                      <label className="text-sm">Nama Obat</label>
                      <FormInput
                        isClearable={false}
                        name={`listObat.${index}.name`}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Masukkan nama obat"
                        value={obat.name}
                        onChange={(e) =>
                          updateObatItem(index, "name", e.target.value)
                        }
                      />
                    </div>
                  </div>
                  {/* Quantity In */}
                  <div className="flex gap-6 ">
                    <div className="flex flex-col gap-1 w-full">
                      <label className="text-sm">Jumlah Masuk</label>
                      <FormInput
                        isClearable={false}
                        name={`listObat.${index}.quantityIn`}
                        isNumeric={true}
                        formik={formik}
                        placeholder="0"
                        value={obat.quantityIn.toString()}
                        onChange={(e) =>
                          updateObatItem(
                            index,
                            "quantityIn",
                            parseInt(e.target.value) || 0
                          )
                        }
                      />
                    </div>

                    {/* Perusahaan */}
                    <div className="flex flex-col gap-1 w-full">
                      <label className="text-sm">Perusahaan</label>
                      <FormInput
                        isClearable={false}
                        name={`listObat.${index}.perusahaan`}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Nama perusahaan"
                        value={obat.perusahaan}
                        onChange={(e) =>
                          updateObatItem(index, "perusahaan", e.target.value)
                        }
                      />
                    </div>
                  </div>

                  {/* Tanggal Expired */}
                  <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
                    <label className="text-sm">Tanggal Expired</label>
                    <FormDatePicker
                      name={`listObat.${index}.tglExpired`}
                      formik={formik}
                      onChange={(value) =>
                        updateObatItem(index, "tglExpired", value)
                      }
                    />
                  </div>
                </div>
              ))}
            </div>
            <div className="flex items-center justify-between">
              <Button
                type="button"
                color="default"
                className="bg-success-100 font-semibold text-green-600"
                size="sm"
                startContent={<Plus size={16} />}
                onPress={addObatItem}
              >
                Tambah Obat
              </Button>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              color="primary"
              size="sm"
              isDisabled={formik.values.listObat.length === 0}
            >
              Simpan Data
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
