"use client";

import { useFormik } from "formik";
import React from "react";
import { postPenambahanObat } from "./schema/postPenambahanObat";
import { FilterDatePicker } from "@/components/custom/datepicker";
import { FormInput } from "@/components/custom/input";
import { DateValue } from "@internationalized/date";

export type listObat = {
  id: string;
  name: string;
  quantityIn: number;
};

type payload = {
  tglMasuk: DateValue | null;
  noBatch: string;
  listObat: Array<string>;
};

export default function PageDetailInventoriObat() {
  const handleSubmit = async (value: payload) => {
    alert(JSON.stringify(value));
  };

  const initialValues = {
    tglMasuk: null,
    noBatch: "",
    listObat: [],
  };
  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: postPenambahanObat,
    onSubmit: handleSubmit,
  });

  return (
    <div className="bg-default-50 flex flex-col gap-6 p-5 rounded-md">
      <h1 className="text-xl font-medium mb-4">
        Form Tambah Penambahan Obat Massal
      </h1>
      <form action="" onSubmit={formik.handleSubmit}>
        <div className="flex items-center gap-6">
          <div className="flex flex-col gap-1 w-full">
            <label htmlFor="poli" className="text-sm ">
              {" "}
              Tanggal Perolehan
            </label>
            <FilterDatePicker name="tglMasuk" formik={formik} />
          </div>
          <div className="flex flex-col gap-1 w-full">
            <label htmlFor="poli" className="text-sm ">
              {" "}
              No. Batch
            </label>
            <FormInput
              isClearable={false}
              name={"noBatch"}
              isNumeric={false}
              formik={formik}
              placeholder="Ketik No. Batch obat masuk"
            />
          </div>
        </div>
      </form>
    </div>
  );
}
