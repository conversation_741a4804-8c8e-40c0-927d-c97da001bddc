import PageHeader from "./_components/headerPage";
import TableDetailLayanan from "./_components/table";

export default async function PageDetailLayanan() {
  const data = { id: "20" };
  return (
    <div className="flex flex-col rounded-md gap-6 lg:py-4 py-4 bg-default-50 p-6">
      <div className="flex flex-col gap-4 border-b-2 pb-6">
        <PageHeader id={data.id} />
        <div className="flex gap-6">
          <p className="font-medium">Pasien Pengguna Layanan dan Tarif : </p>
          <div className="flex flex-col gap-1">
            {Array(5)
              .fill(null)
              .map((_, e) => (
                <div className="flex items-center gap-4 text-sm" key={e}>
                  <p className="font-medium ">Mahasiswa</p>- Rp.40000
                </div>
              ))}
          </div>
        </div>
      </div>
      {Array(2)
        .fill(null)
        .map((_, i) => (
          <TableDetailLayanan />
        ))}
    </div>
  );
}
