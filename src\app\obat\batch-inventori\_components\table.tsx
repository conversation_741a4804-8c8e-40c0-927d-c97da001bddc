"use client";

import UTable from "@/components/custom/table";
import {
  addToast,
  <PERSON><PERSON>,
  Spinner,
  Switch,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { use, useCallback, useState } from "react";
// import { ResponsePoli } from "../_schema/typePoli";
// import Modals from "./modal";
// import ModalPost from "./modalPost";
// import { patchtPoli } from "../actions";
import { useRouter } from "next/navigation";
import Modals from "./modal";
import ModalPost from "./modalPost";
import ModalComp from "./modalPost";

const dummyApi = [
  {
    id: 1,
    noBatch: "123456789",
    tglPerolehan: "12/12/2021",
    jenisObat: "Obat A",
    sumObat: "10",
    almostExpired: "1",
    expired: "0",
  },
  {
    id: 2,
    noBatch: "123456789",
    tglP<PERSON>lehan: "12/12/2021",
    jenisObat: "Obat A",
    sumObat: "10",
    almostExpired: "0",
    expired: "1",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "noBatch",
    label: "No. Batch",
  },
  {
    key: "tglPerolehan",
    label: "Tanggal Perolehan",
  },
  {
    key: "jenisObat",
    label: "Jenis Obat",
  },
  {
    key: "sumObat",
    label: "Jumlah Obat",
  },
  {
    key: "almostExpired",
    label: "Obat Hampir Expired",
  },
  {
    key: "expired",
    label: "Obat Expired",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TableObat({ poli }: Props) {
  const [page, setPage] = useState(0);
  const [sizePage, setSizePage] = useState(10);
  // const data: ResponsePoli = use(poli);
  const router = useRouter();

  const pagination = {
    page: page, // current page
    pageSize: sizePage, // banyak data list
    // totalPage: data?.page?.total_pages ?? 1, //
    // totalData: data?.page?.total_elements ?? 10,
    totalPage: 1, //
    totalData: 10,
    onChangePage: (page: number, pageSize: number) => {
      setPage(page);
      setSizePage(pageSize);
      router.push(`/referensi/poli?page=${page}&size=${pageSize}`);
    },
  };

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    // try {
    //   const res = await patchtPoli({ active: !currentSwitch, id: id });
    //   return res;
    // } catch (error: any) {
    //   addToast({
    //     title: "Error",
    //     color: "danger",
    //     description: error.message ?? "Terjadi kesalahan",
    //   });
    // }
  };

  const renderCell = useCallback(
    (item: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "noBatch":
          return <p className="lg:w-[200px]  ">{item.noBatch}</p>;
        case "tglPerolehan":
          return <p className="lg:w-[200px]  ">{item.tglPerolehan}</p>;
        case "almostExpired":
          return (
            <p
              className={`lg:w-[40px] py-2 px-4 rounded font-medium ${
                item.almostExpired >= 1 ? "bg-yellow-200" : ""
              } `}
            >
              {item.almostExpired}
            </p>
          );
        case "expired":
          return (
            <p
              className={` lg:w-[40px] py-2 px-4 rounded font-medium ${
                item.expired >= 1 ? "bg-danger-200" : ""
              } `}
            >
              {item.almostExpired}
            </p>
          );
        case "action":
          return <Modals id={item.id} />;
        default:
          return item[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Poli</h1>
        <ModalPost />
      </div>
      <UTable pagination={pagination} bordered={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={
                column.key === "action" || column.key === "active"
                  ? "end"
                  : "start"
              }
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={dummyApi ?? []}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {dummyApi.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
      <div className="">
        <p className="font-medium mb-4">Keterangan</p>
        <div className="flex items-center gap-4 mb-2">
          <span className="block bg-danger-200 rounded h-5 w-5" />
          <p className="text-sm">Sudah melewati tanggal kadaluarsa</p>
        </div>
        <div className="flex items-center gap-4">
          <span className="block bg-yellow-200 rounded h-5 w-5" />
          <p className="text-sm">Hampir mendekati tanggal kadaluarsa</p>
        </div>
      </div>
    </div>
  );
}
