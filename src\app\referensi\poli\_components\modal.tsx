"use client";

import { FormInput } from "@/components/custom/input";
import ApiResponse from "@/interfaces/api-response";
import {
  addToast,
  <PERSON>ton,
  Modal,
  ModalBody,
  <PERSON>dalContent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON>eader,
  Skeleton,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Pencil } from "lucide-react";
import { useEffect, useState } from "react";
import useSWR from "swr";
import { poliPost } from "../_schema/postPoli";
import { getPoliById, patchtPoli } from "../actions";

type payload = {
  name: string;
};

export default function Modals({ id }: { id: string }) {
  const [loading, setLoading] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    name: "",
  };

  const handleSubmit = async (value: typeof initialValues) => {
    formik.setSubmitting(true);
    try {
      const res = await patchtPoli({ name: value.name, id });
      if (res.error) {
        throw new Error(res?.message);
      }
      addToast({
        title: "Berhasil update data",
        color: "success",
      });
      window.location.reload();
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    } finally {
      formik.setSubmitting(false);
    }
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: poliPost,
    onSubmit: handleSubmit,
  });

  const getPoli = async () => {
    setLoading(true);
    try {
      const res = (await getPoliById({ id })) as any;
      formik.setFieldValue("name", res?.name);
      return res;
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      getPoli();
    }
  }, [id, isOpen]);

  return (
    <>
      <Tooltip content="Edit Data">
        <Button isIconOnly color="secondary" size="sm" onPress={onOpenChange}>
          <Pencil className="w-4" />
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Poli
                </ModalHeader>
                <ModalBody className="mb-4">
                  {loading ? (
                    <div className="flex flex-col gap-3">
                      <Skeleton className="h-2 w-1/2 rounded-lg" />
                      <Skeleton className="h-3 w-full rounded-lg" />
                    </div>
                  ) : (
                    <div className="flex flex-col gap-1">
                      <label htmlFor="name" className="text-sm ">
                        {" "}
                        Nama Poli
                      </label>
                      <FormInput
                        name={"name"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Ketik nama poli"
                      />
                    </div>
                  )}
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
